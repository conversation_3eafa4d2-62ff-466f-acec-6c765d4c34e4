<template>
  <view>
    <view class="footprint-item" v-for="(item, index) in list" :key="index" @tap="handleItemClick(item)">
      <!-- 剧目/剧场信息 - 移到图片上方 -->
      <view class="show-info-header">
        <view class="show-details">
          <text class="show-name">{{ item.repertoireName || item.theaterName }}</text>
          <text class="show-venue" v-if="item.theaterName && item.repertoireName">{{ item.theaterName }}</text>
          <text class="show-date" v-if="item.showTime">场次：{{ item.showTime }}</text>
        </view>
      </view>

      <!-- 封面图片 -->
      <view class="image-container">
        <u-image
          class="cover-image"
          :height="item.imageHeight || '320rpx'"
          :src="item.coverPicture ? $picFormat(item.coverPicture) : ''"
          bgColor="transparent"
          mode="aspectFill"
          width="100%"></u-image>

        <!-- 图片上的精品标签 -->
        <view class="image-overlay">
          <view class="premium-tag" v-if="item.isPremium !== false">
            <text class="fas fa-crown premium-icon"></text>
            <text class="premium-text">精品</text>
          </view>
        </view>
      </view>

      <!-- 内容区域 -->
      <view class="content-section">
        <!-- 用户内容 -->
        <view class="user-content" v-if="item.content">
          <u-parse class="content-text" :content="item.content" :copyLink="false" :previewImg="true" :showImgMenu="true" :setTitle="false"></u-parse>
        </view>

        <!-- 用户信息 -->
        <view class="user-info">
          <view class="user-avatar">
            <u-image :src="item.userAvatar || $iconFormat('avatar.jpg')" width="48rpx" height="48rpx" mode="aspectFill" bgColor="#F5F5F5"></u-image>
          </view>
          <view class="user-details">
            <text class="user-name">{{ item.userName }}</text>
          </view>
        </view>

        <!-- 互动数据 -->
        <view class="interaction-bar">
          <view class="interaction-item">
            <text class="fas fa-heart interaction-icon"></text>
            <text class="interaction-count">{{ item.followCount || 0 }}</text>
          </view>
          <view class="interaction-item">
            <text class="fas fa-comment interaction-icon"></text>
            <text class="interaction-count">{{ item.replyCount || 0 }}</text>
          </view>
          <view class="interaction-item">
            <text class="fas fa-thumbs-up interaction-icon"></text>
            <text class="interaction-count">{{ item.likeCount || 0 }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script lang="ts">
export default {
  name: 'FootprintItemNew'
}
</script>

<script lang="ts" setup>
import { $iconFormat, $picFormat, $push } from '@/utils/methods'

defineProps({
  list: { type: Array, default: () => [] }
})

// 处理项目点击事件
const handleItemClick = (item: any) => {
  // 所有数据都跳转到足迹详情页面（现在是评论详情页面）
  $push({ name: 'FootprintDetail', params: { id: item.id } })
}
</script>

<style lang="scss" scoped>
.footprint-item {
  background: #ffffff;
  border-radius: 20rpx;
  margin-bottom: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
  border: 1px solid #f5f5f5;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(-4rpx);
    box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  }
}

/* 剧目信息头部 - 移到图片上方 */
.show-info-header {
  display: flex;
  align-items: flex-start;
  padding: 16rpx 16rpx 12rpx 16rpx;
  border-bottom: 1px solid #f5f5f5;
}

.show-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.show-name {
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 32rpx;
  margin-bottom: 4rpx;
}

.show-venue {
  font-size: 22rpx;
  color: #666666;
  line-height: 28rpx;
  margin-bottom: 2rpx;
}

.show-date {
  font-size: 20rpx;
  color: #999999;
  line-height: 26rpx;
}

/* 图片容器 */
.image-container {
  position: relative;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  border-radius: 20rpx 20rpx 0 0;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, transparent 30%);
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  padding: 16rpx;
}

.premium-tag {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 100%);
  border-radius: 12rpx;
  padding: 6rpx 12rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);
}

.premium-icon {
  font-size: 16rpx;
  color: #8b4513;
  margin-right: 4rpx;
}

.premium-text {
  font-size: 20rpx;
  color: #8b4513;
  font-weight: 600;
}

/* 内容区域 */
.content-section {
  padding: 24rpx;
}

.show-info {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.show-name {
  flex: 1;
  font-size: 26rpx;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 32rpx;
}

.show-time {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.time-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  margin-right: 12rpx;

  .fas {
    font-size: 14rpx;
    color: #999999;
  }
}

.time-text {
  font-size: 22rpx;
  color: #666666;
  line-height: 28rpx;
}

.user-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 26rpx;
  color: #333333;
  line-height: 36rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
}

/* 用户信息 */
.user-info {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 16rpx;
  border: 2px solid #f5f5f5;
}

.user-details {
  flex: 1;
}

.user-name {
  display: block;
  font-size: 24rpx;
  font-weight: 500;
  color: #1a1a1a;
  line-height: 28rpx;
  margin-bottom: 4rpx;
}

.create-time {
  font-size: 20rpx;
  color: #999999;
  line-height: 24rpx;
}

/* 互动栏 */
.interaction-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16rpx;
  border-top: 1px solid #f5f5f5;
}

.interaction-item {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  background: #fafbfc;
  border-radius: 16rpx;
  transition: all 0.3s ease;

  &:active {
    background: #f0f1f3;
    transform: scale(0.95);
  }
}

.interaction-icon {
  font-size: 20rpx;
  color: #666666;
  margin-right: 8rpx;
}

.interaction-count,
.interaction-text {
  font-size: 22rpx;
  color: #666666;
  font-weight: 500;
}

/* 特殊状态样式 */
.interaction-item:first-child {
  .interaction-icon {
    color: #ff6b6b;
  }
}

.interaction-item:nth-child(2) {
  .interaction-icon {
    color: #4ecdc4;
  }
}

.interaction-item:last-child {
  .interaction-icon {
    color: #45b7d1;
  }
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .content-section {
    padding: 20rpx;
  }

  .show-name {
    font-size: 24rpx;
  }

  .content-text {
    font-size: 24rpx;
    line-height: 34rpx;
  }

  .interaction-item {
    padding: 6rpx 12rpx;
  }

  .interaction-count,
  .interaction-text {
    font-size: 20rpx;
  }
}

/* 加载动画 */
@keyframes slideInUp {
  from {
    transform: translateY(30rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.footprint-item {
  animation: slideInUp 0.6s ease-out;
}
</style>
