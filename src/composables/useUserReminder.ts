import { $getUserReminder, $saveOrUpdateUserReminder, $updateTheaterFund, type UserReminderRequest, type UserReminderResponse } from '@/api/userReminder'
import dayjs from 'dayjs'
import { computed, readonly, ref } from 'vue'

export const useUserReminder = () => {
  const data = ref<UserReminderResponse | null>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 获取用户提醒设置
  const refresh = async () => {
    loading.value = true
    error.value = null
    try {
      const response = await $getUserReminder()
      if (response.code === 200) {
        data.value = response.data
      } else {
        error.value = response.msg || '获取数据失败'
      }
    } catch (err: any) {
      error.value = err.msg || '网络请求失败'
      console.error('获取用户提醒设置失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 保存用户提醒设置
  const save = async (requestData: UserReminderRequest): Promise<boolean> => {
    try {
      const response = await $saveOrUpdateUserReminder(requestData)
      if (response.code === 200) {
        await refresh() // 刷新数据
        return true
      } else {
        error.value = response.msg || '保存失败'
        return false
      }
    } catch (err: any) {
      error.value = err.msg || '保存失败'
      console.error('保存用户提醒设置失败:', err)
      return false
    }
  }

  // 更新看剧基金
  const updateFund = async (current: number, target?: number): Promise<boolean> => {
    try {
      const params: any = { currentAmount: current.toString() }
      if (target !== undefined) {
        params.targetAmount = target.toString()
      }

      const response = await $updateTheaterFund(params)
      if (response.code === 200) {
        await refresh() // 刷新数据
        return true
      } else {
        error.value = response.msg || '更新失败'
        return false
      }
    } catch (err: any) {
      error.value = err.msg || '更新失败'
      console.error('更新看剧基金失败:', err)
      return false
    }
  }

  // 计算详细的倒计时信息（天时分秒）
  const calculateDetailedCountdown = (targetTime: any) => {
    const now = dayjs()
    const target = dayjs(targetTime)
    const diff = target.valueOf() - now.valueOf()

    if (diff <= 0) return null

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    return { days, hours, minutes, seconds, totalDays: days }
  }

  // 计算倒计时数据 - 用于首页显示
  const countdownData = computed(() => {
    if (!data.value) return null

    const reminders = []
    const now = dayjs()

    // 发薪日倒计时
    if (data.value.salaryDay) {
      const salaryDay = data.value.salaryDay
      const currentDate = now.date()

      // 计算下次发薪日
      let nextSalaryDate = now.date(salaryDay).hour(9).minute(0).second(0) // 假设9点发薪
      if (currentDate >= salaryDay) {
        // 如果本月发薪日已过，计算下月
        nextSalaryDate = now.add(1, 'month').date(salaryDay).hour(9).minute(0).second(0)
      }

      const diffDays = nextSalaryDate.diff(now, 'day')

      reminders.push({
        type: 'salary',
        title: '发薪倒计时',
        days: diffDays,
        value: `${diffDays}天`,
        priority: diffDays,
        targetTime: nextSalaryDate
      })
    }

    // 开演倒计时
    if (data.value.showTime) {
      const countdown = calculateDetailedCountdown(data.value.showTime)

      if (countdown) {
        reminders.push({
          type: 'showTime',
          title: '开演倒计时',
          days: countdown.totalDays,
          hours: countdown.hours,
          minutes: countdown.minutes,
          seconds: countdown.seconds,
          value: countdown.totalDays > 0 ? `${countdown.totalDays}天` : `${countdown.hours}时${countdown.minutes}分`,
          detailedValue: `${countdown.days}天${countdown.hours}时${countdown.minutes}分${countdown.seconds}秒`,
          priority: countdown.totalDays,
          targetTime: data.value.showTime,
          hasDetailedTime: true
        })
      }
    }

    // 开票倒计时
    if (data.value.ticketSaleTime) {
      const countdown = calculateDetailedCountdown(data.value.ticketSaleTime)

      if (countdown) {
        reminders.push({
          type: 'ticketSale',
          title: '开票倒计时',
          days: countdown.totalDays,
          hours: countdown.hours,
          minutes: countdown.minutes,
          seconds: countdown.seconds,
          value: countdown.totalDays > 0 ? `${countdown.totalDays}天` : `${countdown.hours}时${countdown.minutes}分`,
          detailedValue: `${countdown.days}天${countdown.hours}时${countdown.minutes}分${countdown.seconds}秒`,
          priority: countdown.totalDays,
          targetTime: data.value.ticketSaleTime,
          hasDetailedTime: true
        })
      }
    }

    // 看剧基金进度
    if (data.value.theaterFundCurrentAmount !== undefined) {
      const current = data.value.theaterFundCurrentAmount
      const target = data.value.theaterFundTargetAmount || 0
      const progress = target > 0 ? Math.round((current / target) * 100) : 0

      reminders.push({
        type: 'fund',
        title: '看剧基金',
        value: `¥${current.toLocaleString()}`, // 显示当前金额
        progress: progress,
        priority: 999 // 基金优先级较低
      })
    }

    // 按优先级排序（天数越少优先级越高）
    reminders.sort((a, b) => a.priority - b.priority)

    return {
      main: reminders[0] || null, // 主要显示的倒计时
      others: reminders.slice(1) // 其他倒计时
    }
  })

  return {
    data: readonly(data),
    loading: readonly(loading),
    error: readonly(error),
    countdownData,
    refresh,
    save,
    updateFund
  }
}
