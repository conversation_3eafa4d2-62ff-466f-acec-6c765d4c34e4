<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-gradient-to-b from-bgS to-bgE bg-fullAuto bg-no-repeat">
    <mescroll-uni class="block h-0 w-screen grow" :down="downOpt1" :fixed="false" :up="upOpt1" @down="upCallback" @init="mescrollInit">
      <u-navbar class="w-full" :fixed="false" bgColor="transparent" leftIcon=" " placeholder />

      <view class="w-full pb-[200rpx] pt-[120rpx]">
        <!-- 用户信息 -->
        <view class="userBaseInfo w-full">
          <!-- ip地址 -->
          <view class="mb-[10rpx] flex items-center justify-end pr-[36rpx]">
            <image class="mr-0.5 block h-[32rpx] w-[32rpx]" :src="$iconFormat('icon/address.svg')" mode="scaleToFill" />
            <text class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-100">IP属地: {{ idAddress || userInfo.ipaddr || '未知' }}</text>
          </view>

          <view class="mb-[14rpx] w-full rounded-t-[30rpx] bg-[#201A34] pb-[40rpx] pl-[24rpx] pr-[24rpx] pt-[15rpx]">
            <!-- 头像昵称 -->
            <view class="relative mb-[50rpx] box-border flex w-full items-start justify-start pl-[152rpx]">
              <!-- 头像 -->
              <view
                class="avatar absolute bottom-0 left-0 box-content flex h-[144rpx] w-[144rpx] shrink-0 items-center justify-center rounded-full bg-gradient-to-b from-lightPurple to-purple p-[5rpx]">
                <view class="h-[144rpx] w-[144rpx] overflow-hidden rounded-full bg-[#513D74] align-middle">
                  <u-avatar :defaultUrl="$iconFormat('avatar.jpg')" :src="userInfo.avatarUrl" size="144rpx"></u-avatar>
                </view>

                <!-- 性别 -->
                <image class="absolute bottom-0 right-0 block h-[36rpx] w-[36rpx]" :src="$iconFormat('icon/male.svg')" mode="scaleToFill" v-if="userInfo.sex == 0" />
                <image class="absolute bottom-0 right-0 block h-[36rpx] w-[36rpx]" :src="$iconFormat('icon/female.svg')" mode="scaleToFill" v-else-if="userInfo.sex == 1" />
              </view>

              <view class="grow">
                <!-- 未登录 -->
                <view class="ml-[30rpx] flex grow items-center justify-between" v-if="!token">
                  <text class="grow font-Medium text-lg font-medium leading-6 text-w-100" @tap="$push({ name: 'Login' })">点击登录</text>

                  <view class="ml-[20rpx] h-6 w-6 shrink-0 rounded-full bg-w-10" @tap="$push({ name: 'Login' })">
                    <image class="block h-6 w-6" :src="$iconFormat('icon/setting.svg')" mode="scaleToFill" />
                  </view>
                </view>
                <!-- 已登录 -->
                <view class="ml-[20rpx] flex grow items-center justify-between" v-else>
                  <view class="flex grow items-center justify-start" @tap="$push({ name: 'UserInfo' })">
                    <medal class="mr-2" :color="userInfo.rankMedalColor" :level="userInfo.rankMedalLevel" :text="userInfo.rankMedalName" />

                    <text class="line-clamp-1 break-all font-Medium text-lg font-medium leading-6 text-w-100">{{ userInfo.name || '-' }}</text>

                    <image class="ml-2 block h-4 w-4 shrink-0" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
                  </view>

                  <view class="flex shrink-0 items-center justify-end">
                    <view class="relative ml-[20rpx] h-6 w-6 shrink-0 rounded-full bg-w-10" @tap="$push({ name: 'MsgList' })">
                      <image class="block h-6 w-6" :src="$iconFormat('icon/msg.svg')" mode="scaleToFill" />
                      <view
                        class="absolute right-[-10rpx] top-[-10rpx] h-5 min-w-[40rpx] rounded-full bg-red text-center font-Regular text-[26rpx] font-normal leading-4 text-w-100"
                        v-if="msgNum && notifySetting.messageNotify">
                        {{ msgNum }}
                      </view>
                    </view>

                    <view class="ml-[20rpx] h-6 w-6 shrink-0 rounded-full bg-w-10" @tap="$push({ name: 'Setting' })">
                      <image class="block h-6 w-6" :src="$iconFormat('icon/setting.svg')" mode="scaleToFill" />
                    </view>
                  </view>
                </view>

                <!-- 个人简介 -->
                <view
                  class="introduce relative ml-[20rpx] mt-[16rpx] flex min-h-[60rpx] w-fit min-w-[160rpx] items-center justify-center rounded-full bg-w-10 pb-[6rpx] pl-2.5 pr-2.5 pt-[6rpx] text-center font-Regular text-[26rpx] font-normal leading-[36rpx] text-w-60"
                  @tap="$push({ name: 'UserInfo' })">
                  {{ userInfo.personalizedSignature || '请输入个性签名' }}
                </view>
              </view>
            </view>

            <!-- 统计数据 -->
            <view class="flex w-full items-center justify-center">
              <view class="relative flex grow flex-col items-center justify-center" @tap="$push({ name: 'TicketList' })">
                <text class="mb-2.5 font-Medium text-[36rpx] font-medium leading-4 text-w-100">{{ ticketNum }}</text>
                <text class="font-Regular text-[28rpx] font-normal leading-4 text-w-60">电子票夹</text>
                <view class="absolute right-5 top-[-10rpx] mr-2.5 h-1.5 w-1.5 rounded-full bg-red" v-if="dot1"></view>
              </view>
              <view class="h-[34rpx] w-[1rpx] shrink-0 bg-w-10"></view>
              <view class="relative flex grow flex-col items-center justify-center" @tap="$push({ name: 'AvatarList' })">
                <text class="mb-2.5 font-Medium text-[36rpx] font-medium leading-4 text-w-100">{{ avatarNum }}</text>
                <text class="font-Regular text-[28rpx] font-normal leading-4 text-w-60">数字头像</text>
                <view class="absolute right-5 top-[-10rpx] mr-2.5 h-1.5 w-1.5 rounded-full bg-red" v-if="dot2"></view>
              </view>
              <view class="h-[34rpx] w-[1rpx] shrink-0 bg-w-10"></view>
              <view class="re relative flex grow flex-col items-center justify-center" @tap="$push({ name: 'BadgeList' })">
                <text class="mb-2.5 font-Medium text-[36rpx] font-medium leading-4 text-w-100">{{ badgeNUm }}</text>
                <text class="font-Regular text-[28rpx] font-normal leading-4 text-w-60">纪念徽章</text>
                <view class="absolute right-5 top-[-10rpx] mr-2.5 h-1.5 w-1.5 rounded-full bg-red" v-if="dot3"></view>
              </view>
            </view>
          </view>
        </view>

        <!-- 菜单导航 -->
        <view class="w-full bg-[#201A34]" v-if="token">
          <template :key="index" v-for="(item, index) in menuList">
            <view class="flex h-[100rpx] w-full items-center justify-start pl-3 pr-3" @tap="$push({ name: item.linkName })">
              <image class="mr-2.5 block h-5 w-5 shrink-0" :src="$iconFormat(`menu/personal${index + 1}.svg`)" mode="scaleToFill" />
              <text class="mr-2.5 grow font-Regular text-[30rpx] font-normal leading-5 text-w-100">{{ item.meunName }}</text>

              <view class="mr-2.5 h-1.5 w-1.5 rounded-full bg-red" v-if="item.dot && item.showCount"></view>
              <view class="mr-2.5 font-Regular text-xs font-normal leading-5 text-w-60" v-if="item.wait && item.showCount">{{ item.wait }}部待评价</view>
              <view class="mr-2.5 font-Regular text-xs font-normal leading-5 text-w-60" v-if="item.update && item.showCount">{{ item.update }}更新</view>
              <view class="mr-2.5 h-5 min-w-[40rpx] rounded-full bg-red text-center font-Regular text-[26rpx] font-normal leading-5 text-w-100" v-if="item.num && item.showCount">{{ item.num }}</view>

              <image class="block h-4 w-4" :src="$iconFormat('arrow/right1.svg')" mode="scaleToFill" />
            </view>
            <view class="m-auto h-[1rpx] w-[702rpx] bg-w-10" v-if="index != menuList.length - 1"></view>
          </template>
        </view>
      </view>
    </mescroll-uni>

    <!-- 底部导航 -->
    <tabbar name="Personal" />
  </view>

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
import { $commentCount, $dynamicCount, $findNotLookCount, $notLookRankCount, $receivingRecordsCount, $userMessageNotifyCount, $userNotLookNotifyCount } from '@/api/count'
import { $userOrderNum } from '@/api/order'
import { $leaderboardLastTime } from '@/api/rank'
import { $collectionNum, $digitalAvatarCount } from '@/api/scan'
import { $userSetting } from '@/api/setting'
import medal from '@/components/Medal.vue'
import network from '@/components/Network.vue'
import tabbar from '@/components/Tabbar.vue'
import { useGlobalStore } from '@/stores/global'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { $iconFormat, $push } from '@/utils/methods'
import QQMapWX from '@jonny1994/qqmap-wx-jssdk'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

const app: any = getCurrentInstance()?.proxy
const globalStore = useGlobalStore()
const { userInfo, downOpt1, upOpt1, token } = storeToRefs(useGlobalStore())
const { mescrollInit } = useMescroll(onPageScroll, onReachBottom)

const dot1 = ref(false)
const dot2 = ref(false)
const dot3 = ref(false)

const lastTime = ref('')
const msgNum = ref(0) // 消息数量
const ticketNum = ref(0) // 电子票数量
const avatarNum = ref(0) // 数字头像数量
const badgeNUm = ref(0) // 纪念徽章数量

const notifySetting = ref() // 设置

/* 导航菜单 */
const menuList = reactive<any>([
  { meunName: '我的榜单', linkName: 'Rank', wait: 0, showCount: true },
  { meunName: '观演历史', linkName: 'HistoryList', wait: 0, showCount: true },
  { meunName: '我的订单', linkName: 'OrderList', num: 0, showCount: true },
  { meunName: '我的勋章', linkName: 'MedalList', dot: false, showCount: true },
  { meunName: '我的关注', linkName: 'FollowList', num: 0, showCount: true },
  { meunName: '互动消息', linkName: 'InteractionList', num: 0, showCount: true },
  { meunName: '私信消息', linkName: 'MassList', num: 0, showCount: true },
  { meunName: '物料信息', linkName: 'MaterialManage', num: 0, showCount: false }
  // { meunName: '我的评论', linkName: 'CommentList', num: 0, showCount: true },
])

let qqmapsdk: any
const idAddress = ref('')

onShow(() => {
  if (globalStore.token) handleGetNum()

  qqmapsdk = new QQMapWX({ key: 'N5ZBZ-W53KI-WSWGE-5X22E-Z46WH-OIFA5' })

  qqmapsdk.reverseGeocoder({
    success: (res: any) => {
      idAddress.value = res.result.ad_info.city
    }
  })
})

/* 数据加载 */
const upCallback = async (mescroll: any) => {
  try {
    if (globalStore.token) {
      await globalStore.handleRefreshUserInfo()

      uni.$emit('updateTabbarNum')

      handleGetNum()
    }

    mescroll.endSuccess(1, false)
  } catch (error) {
    mescroll.endErr() // 请求失败, 结束加载
  }
}

/* 获取消息数量 */
const handleGetNum = async () => {
  /* 我的榜单 */
  await $leaderboardLastTime().then((res: any) => {
    let data: string = res?.data ? dayjs(res.data).fromNow() : ''
    menuList[0].update = data
  })

  // 电子票夹
  dot1.value = (await $findNotLookCount(1)).data > 0
  // 数字头像
  dot2.value = (await $findNotLookCount(2)).data > 0
  // 纪念勋章
  dot3.value = (await $findNotLookCount(3)).data > 0

  // 推送消息
  msgNum.value = (await $userNotLookNotifyCount()).data

  // 电子票夹
  ticketNum.value = (await $collectionNum({ userId: userInfo.value.id, badgeType: 1 })).data
  // 数字头像
  avatarNum.value = (await $digitalAvatarCount()).data
  // 纪念勋章
  badgeNUm.value = (await $collectionNum({ userId: userInfo.value.id, badgeType: 3 })).data

  notifySetting.value = (await $userSetting()).data

  // 观演历史
  menuList[1].showCount = notifySetting.value.commentNotify
  // 我的勋章
  menuList[3].showCount = notifySetting.value.likeNotify
  // 我的关注
  menuList[4].showCount = notifySetting.value.likeNotify
  // 互动消息
  menuList[5].showCount = notifySetting.value.wellNotify || notifySetting.value.issueNotify || notifySetting.value.commentNotify
  // 群发消息
  menuList[6].showCount = notifySetting.value.groupMessageNotify

  // 观演历史
  menuList[1].wait = (await $commentCount({ userId: userInfo.value.id, commentFlag: 0 })).data
  // 我的订单
  menuList[2].num = (await $userOrderNum()).data
  // 我的勋章
  menuList[3].dot = (await $notLookRankCount()).data > 0 ? true : false
  // 我的关注
  menuList[4].num = (await $dynamicCount()).data
  // 互动消息
  let num1 = (await $receivingRecordsCount(2)).data
  let num2 = (await $receivingRecordsCount(3)).data
  let num3 = (await $receivingRecordsCount(1)).data
  menuList[5].num = (notifySetting.value.wellNotify ? num1 : 0) + (notifySetting.value.issueNotify ? num2 : 0) + (notifySetting.value.commentNotify ? num3 : 0)
  // 群发消息
  menuList[6].num = (await $userMessageNotifyCount()).data
}
</script>

<style lang="scss" scoped>
.pageWrap {
  background-image: url($icon + 'background/personal.webp');

  .userBaseInfo {
    .introduce {
      &::before {
        @apply absolute left-[30%] top-[-12rpx] m-auto block h-0 w-0 border-b-[12rpx] border-l-[6rpx] border-r-[6rpx] border-solid border-b-w-10 border-l-transparent border-r-transparent;

        content: '';
      }
    }
  }
}
</style>
