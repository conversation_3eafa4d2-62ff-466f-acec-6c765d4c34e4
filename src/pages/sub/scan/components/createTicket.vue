<template>
  <!-- 电子票合成 -->
  <l-painter
    :css="`width: 520rpx; height: ${ticketInfo?.ticketH}; border-radius: 40rpx;`"
    :pixelRatio="2"
    @fail="handleGetTicketFail"
    @success="handleGetTicket"
    hidden
    isCanvasToTempFilePath
    pathType="url"
    v-if="ticketInfo && userInfo">
    <!-- 电子票背景 -->
    <l-painter-image :src="ticketInfo?.coverFrontUrl" css="width: 100%; height: 100%; object-fit: cover; border-radius: 40rpx;" />

    <!-- 顶部内容 -->
    <l-painter-view :css="`width: 100%; height: 150rpx; position: absolute; top: 0; left:0; border-radius: 40rpx 40rpx 0 0;`">
      <!-- 顶部遮罩 -->
      <!-- <l-painter-image
        :src="$iconFormat('background/ticketCoverT.webp')"
        css="width: 100%; height: 150rpx; object-fit: cover; position: absolute; top: 0; left:0;" /> -->

      <l-painter-view :css="`width: 448rpx; position: absolute; top: 0; left:0; padding: 36rpx 36rpx 0 36rpx;`">
        <!-- 剧场头像 -->
        <l-painter-image :src="ticketInfo?.repertoireCoverPictureUrl" css="width: 37rpx; height: 37rpx; display: inline-block; object-fit: cover; border-radius: 50%;" />

        <!-- 剧目名称 -->
        <l-painter-text :text="`${ticketInfo?.repertoire}电子票根`" css="width: 401rpx; margin: 0 0 0 10rpx; color: #FFFFFF; line-height: 37rpx; font-weight: normal; font-size: 28rpx;" />

        <!-- 专属编号 -->
        <l-painter-text
          :text="ticketInfo?.serialNumber"
          css="height: 34rpx; padding: 0 10rpx; margin: 10rpx 0 0 0; color: #392584; line-height: 34rpx; font-weight: normal; font-size: 26rpx; background: #E6DAFF; border-radius: 17rpx;" />
      </l-painter-view>
    </l-painter-view>

    <l-painter-view v-if="ticketInfo?.ifCompositeRepertoireName" :css="`width: 90%; height: 150rpx; position: absolute; top: 430rpx; left:50rpx; border-radius: 40rpx 40rpx 0 0;`">
      <!-- 剧目名称 -->
      <l-painter-text :text="`${ticketInfo?.repertoire}`" css="width: 401rpx; margin: 0 0 0 10rpx; color: #FFFFFF; line-height: 40rpx; font-weight: bolder; font-size: 30rpx;" />
    </l-painter-view>

    <!-- 底部内容 -->
    <l-painter-view :css="`width: 100%; height: 313rpx; position: absolute; bottom: 0; left:0; border-radius: 0 0 40rpx 40rpx;`">
      <!-- 底部遮罩 -->
      <!-- <l-painter-image
        :src="$iconFormat('background/ticketCoverB.webp')"
        css="width: 100%; height: 214rpx; object-fit: cover; position: absolute; bottom: 0; left:0;" /> -->

      <!-- 用户头像 -->
      <l-painter-image :src="userInfo?.avatarUrl" css="width: 59rpx; height: 59rpx; object-fit: cover; position: absolute; top: 0; left: 34rpx; display: block; border-radius: 50%;" />

      <!-- 用户昵称 -->
      <l-painter-text :text="userInfo?.name" css="position: absolute; top: 8rpx; left: 106rpx; color: #FFFFFF; line-height: 44rpx; font-weight: normal; font-size: 32rpx;" />

      <!-- 演出时间 | 演出地点 -->
      <l-painter-text
        :text="`${ticketInfo?.dateTime} | ${ticketInfo?.theater}`"
        css="position: absolute; bottom: 170rpx; left: 18rpx; width: 484rpx; text-align: center; color: #FFFFFF; line-height: 28rpx; font-weight: normal; font-size: 24rpx;" />

      <!-- 横线 -->
      <l-painter-view css="position: absolute; bottom: 146rpx; left: 34rpx; width: 446rpx; height: 2rpx; background: rgba(255, 255, 255, 0.2);"></l-painter-view>

      <!-- 票价 -->
      <l-painter-text css="position: absolute; bottom: 86rpx; left: 34rpx; color: rgba(255, 255, 255, 0.6); line-height: 34rpx; font-weight: normal; font-size: 24rpx;" text="1张 | 总票价" />

      <!-- 座位 -->
      <l-painter-text css="position: absolute; bottom: 86rpx; left: 208rpx; color: rgba(255, 255, 255, 0.6); line-height: 34rpx; font-weight: normal; font-size: 24rpx;" text="座位" />

      <!-- 总票价 -->
      <l-painter-view css="position: absolute; bottom: 22rpx; left: 34rpx;">
        <l-painter-text css="display: inline-block; margin: 12rpx 0 0 0; color: #FFFFFF; line-height: 34rpx; font-weight: normal; font-size: 24rpx;" text="￥" />
        <!-- Number(ticketInfo?.price?.split('元')[0]) -->
        <l-painter-text :text="ticketPrice" css="display: inline-block; color: #FFFFFF; line-height: 56rpx; font-weight: bold; font-size: 28rpx;" />
      </l-painter-view>

      <!-- 座位 -->
      <l-painter-text
        :text="ticketInfo?.seat"
        css="position: absolute; bottom: 32rpx; left: 200rpx; color: #FFFFFF; line-height: 40rpx; font-weight: normal; font-size: 28rpx;"
        v-if="ticketInfo?.seat?.length <= 10" />
      <l-painter-text
        :text="ticketInfo?.seat"
        css="width: 300rpx; position: absolute; bottom: 12rpx; left: 200rpx; min-height: 68rpx; color: #FFFFFF; line-height: 34rpx; font-weight: normal; font-size: 22rpx;"
        v-else />
    </l-painter-view>
  </l-painter>
</template>

<script lang="ts">
export default { options: { styleIsolation: 'shared', virtualHost: true } }
</script>

<script lang="ts" setup>
import { $upload } from '@/api/base'
import { useGlobalStore } from '@/stores/global'
import { $toast } from '@/utils/methods'

const emit = defineEmits(['getUrl'])
const props = defineProps({
  app: { type: Object, default: () => {} },
  ticketInfo: { type: Object, default: () => {} }
})

const { userInfo } = storeToRefs(useGlobalStore())

/* 票价 */
const ticketPrice = computed(() => {
  let price: any = '0'

  if (props.ticketInfo?.price) {
    let temp: any = props.ticketInfo?.price

    temp = temp.replace(/元/g, '')
    temp = temp.split('￥').join('')
    temp = temp.split('¥').join('')

    if (!isNaN(Number(temp))) temp = Number(temp) - 0

    if (temp === 0) temp = temp + ''

    price = temp
  }

  return price
})

/* 获取生成的电子票 */
const handleGetTicket = (val: any) => {
  $upload({
    name: 'file',
    filePath: val
  }).then((res: any) => {
    emit('getUrl', res.fileName)

    uni.hideLoading()
  })
}

/* 生成失败 */
const handleGetTicketFail = (err: any) => {
  $toast(props.app, '生成电子票失败')
  uni.hideLoading()
}
</script>

<style lang="scss" scoped></style>
