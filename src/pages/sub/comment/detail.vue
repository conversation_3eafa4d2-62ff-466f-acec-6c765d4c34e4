<template>
  <view class="pageWrap flex h-screen w-screen flex-col items-start justify-start bg-[#1F1933] bg-fullAuto bg-no-repeat">
    <!-- 导航栏 -->
    <u-navbar class="w-full shrink-0" @leftClick="$back" bgColor="transparent" leftIconColor="#FFFFFF" placeholder>
      <template #center>
        <view class="flex w-[574rpx] items-center justify-start pr-[80rpx]">
          <u-image class="shrink-0" :src="commentDetail.userAvatar" bgColor="transparent" height="72rpx" mode="aspectFill" radius="50%" width="72rpx"></u-image>
          <view class="ml-2 grow">
            <view class="mb-[2rpx] line-clamp-1 font-Medium text-[32rpx] font-medium leading-[44rpx] text-w-100">{{ commentDetail.userName }}</view>
          </view>
        </view>
      </template>
    </u-navbar>

    <view class="mt-[10rpx] flex w-full grow flex-col items-start justify-start rounded-t-[30rpx] bg-[#1F1933] pt-2.5">
      <!-- 剧场信息 -->
      <view
        class="m-auto mb-2.5 flex w-[682rpx] shrink-0 items-start justify-start rounded-[20rpx] bg-deepbg pb-1 pl-[10rpx] pr-[10rpx] pt-1"
        @tap="$push({ name: 'TheaterDetail', params: { id: repertoireId } })"
        v-if="type === 1">
        <u-image class="shrink-0" :src="commentDetail.theaterCoverPicture" bgColor="transparent" height="80rpx" mode="aspectFill" radius="10rpx" width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{ commentDetail.theaterName || '-' }}</view>
          <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60">地址:{{ commentDetail.address || '-' }}</view>
        </view>
      </view>

      <!-- 剧目信息 -->
      <view
        class="m-auto mb-2.5 flex w-[682rpx] shrink-0 items-start justify-start rounded-[20rpx] bg-deepbg pb-1 pl-[10rpx] pr-[10rpx] pt-1"
        @tap="$push({ name: 'RepertoireDetail', params: { id: commentDetail.id } })"
        v-else-if="[2, 3].includes(type)">
        <u-image class="shrink-0" :src="commentDetail.repertoireCoverPicture" bgColor="transparent" height="80rpx" mode="aspectFill" radius="10rpx" width="80rpx"></u-image>

        <view class="ml-2.5 grow">
          <view class="mb-[4rpx] font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{ commentDetail.repertoireName || '-' }}</view>
          <view class="font-Regular text-[22rpx] font-normal leading-[32rpx] text-w-60">演出时间:{{ commentDetail.startTime || '-' }} | 演出场地:{{ commentDetail.theaterName || '-' }}</view>
        </view>
      </view>

      <!-- 评价内容 -->
      <mescroll-uni class="block h-0 w-full grow" :down="downOpt1" :fixed="false" :up="upOpt3" @down="downCallback" @init="mescrollInit" @topclick="$topclick" @up="upCallback">
        <view class="m-auto w-[702rpx]">
          <template v-if="type === 2">
            <!-- 剧目点赞 -->
            <view class="mb-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1" v-if="[0, 1].includes(commentDetail.repertoireKudos)">
              <view class="flex w-full items-center justify-start">
                <template v-if="commentDetail.repertoireKudos === 1">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp2.svg')" mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">赞了 {{ commentDetail.repertoireName || '-' }}</view>
                </template>
                <template v-else-if="commentDetail.repertoireKudos === 0">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp4.svg')" mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">踩了 {{ commentDetail.repertoireName || '-' }}</view>
                </template>
              </view>
            </view>
            <u-parse class="mb-2 break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100" :content="commentDetail.content"></u-parse>

            <view class="m-auto mt-3 h-[2rpx] w-full bg-w-10"></view>

            <!-- 剧场点赞 -->
            <view class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1" v-if="[0, 1].includes(commentDetail.theaterKudos)">
              <view class="flex w-full items-center justify-start">
                <template v-if="commentDetail.theaterKudos === 1">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp2.svg')" mode="scaleToFill" />
                  <view class="ml line-clamp-1 grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">赞了 {{ commentDetail.theaterName || '-' }}</view>
                </template>
                <template v-else-if="commentDetail.theaterKudos === 0">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp4.svg')" mode="scaleToFill" />
                  <view class="ml line-clamp-1 grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">踩了 {{ commentDetail.theaterName || '-' }}</view>
                </template>
              </view>
            </view>
            <u-parse class="mb-2 break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100" :content="commentDetail.theaterContent"></u-parse>
          </template>

          <template v-if="type === 1">
            <!-- 剧场点赞 -->
            <view class="mb-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1" v-if="[0, 1].includes(commentDetail.theaterKudos)">
              <view class="flex w-full items-center justify-start">
                <template v-if="commentDetail.theaterKudos === 1">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp2.svg')" mode="scaleToFill" />
                  <view class="ml line-clamp-1 grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">赞了 {{ commentDetail.theaterName || '-' }}</view>
                </template>
                <template v-else-if="commentDetail.theaterKudos === 0">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp4.svg')" mode="scaleToFill" />
                  <view class="ml line-clamp-1 grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">踩了 {{ commentDetail.theaterName || '-' }}</view>
                </template>
              </view>
            </view>
            <u-parse class="mb-2 break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100" :content="commentDetail.theaterContent"></u-parse>

            <view class="m-auto mt-3 h-[2rpx] w-full bg-w-10"></view>

            <!-- 剧目点赞 -->
            <view class="mb-3 mt-3 w-full bg-w-5 pb-1 pl-[18rpx] pr-[18rpx] pt-1" v-if="[0, 1].includes(commentDetail.repertoireKudos)">
              <view class="flex w-full items-center justify-start">
                <template v-if="commentDetail.repertoireKudos === 1">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp2.svg')" mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">赞了 {{ commentDetail.repertoireName || '-' }}</view>
                </template>
                <template v-else-if="commentDetail.repertoireKudos === 0">
                  <image class="mr-[10rpx] block h-[20rpx] w-[20rpx] shrink-0" :src="$iconFormat('icon/thumbsUp4.svg')" mode="scaleToFill" />
                  <view class="ml grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-40">踩了 {{ commentDetail.repertoireName || '-' }}</view>
                </template>
              </view>
            </view>
            <u-parse class="mb-2 break-all text-justify font-Regular text-[32rpx] font-normal leading-[40rpx] text-w-100" :content="commentDetail.content"></u-parse>
          </template>

          <view class="mt-[12rpx] flex items-center justify-start">
            <text class="grow font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">{{ commentDetail.createTime }}</text>
          </view>

          <view class="mb-2 mt-[10rpx] h-[2rpx] w-full bg-w-10"></view>

          <view class="mb-2.5 w-full font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-100">{{ commentDetail.replyCount || 0 }}评论</view>

          <!-- 空状态 -->
          <view class="emptyWrap w-full pt-[200rpx]" v-if="!list || !list.length">
            <u-empty :icon="$iconFormat('empty/comment.svg')" height="322rpx" mode="data" text="暂无评论，去发表第一个评论吧" width="472rpx"></u-empty>
          </view>

          <!-- 回复列表 -->
          <view class="m-auto w-[702rpx] pb-[100rpx]">
            <!-- 回复 i -->
            <view class="mb-2.5 flex w-full items-start justify-start border-b-[1rpx] border-solid border-w-10 pb-2.5 last:mb-0" :key="i.id" v-for="i in list">
              <!-- 头像 -->
              <u-avatar class="mr-2.5 shrink-0" :defaultUrl="$iconFormat('avatar.jpg')" :src="$picFormat(i.replyAvatar)" size="60rpx"></u-avatar>

              <!-- 问题信息 -->
              <view class="grow">
                <view class="mb-[10rpx] flex w-full items-center justify-start">
                  <!-- 用户名 -->
                  <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ i.replyName || '-' }}</text>
                  <!-- 勋章 -->
                  <medal class="ml-1.5" :color="i.rankMedalColor" :level="i.rankMedalLevel" :text="i.rankMedalName" />

                  <image class="ml-[4rpx] mr-[10rpx] block h-2 w-[18rpx]" :src="$iconFormat('arrow/right3.svg')" mode="scaleToFill" v-if="i.userName" />
                  <!-- 回复用户 -->
                  <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60" v-if="i.userName">{{ i.userName || '-' }}</text>
                </view>

                <!-- 回复内容 -->
                <view class="mb-[10rpx] w-full break-all text-justify font-Medium text-[32rpx] font-medium leading-[40rpx] text-w-100">
                  <text>{{ i.content }}</text>
                </view>

                <!-- 时间 -->
                <view class="flex items-center justify-between">
                  <view class="flex shrink-0 items-center justify-start">
                    <view class="shrink-0 font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ i.createTime }}</view>
                    <view class="leding-[32rpx] ml-2.5 shrink-0 font-Medium text-[24rpx] font-medium text-w-80" @tap="handleReply(i)">回复</view>
                  </view>

                  <!-- 赞/踩 -->
                  <view class="ml-[22rpx] flex shrink-0 items-center justify-end">
                    <view class="mr-[32rpx] flex shrink-0 items-center justify-end" @tap="handleKudos(i.id, i.kudosStatus == 1 ? undefined : 1)">
                      <image class="mr-[4rpx] block h-[30rpx] w-[30rpx]" :src="$iconFormat('icon/thumbsUp2.svg')" mode="scaleToFill" v-if="i.kudosStatus === 1" />
                      <image class="mr-[4rpx] block h-[30rpx] w-[30rpx]" :src="$iconFormat('icon/thumbsUp1.svg')" mode="scaleToFill" v-else />
                      <text class="font-Regular text-[24rpx] font-normal leading-[32rpx]" :class="i.kudosStatus === 1 ? 'text-[#FF8A03]' : 'text-w-60'">{{ i.likeCount || 0 }}</text>
                    </view>

                    <view class="flex shrink-0 items-center justify-end" @tap="handleKudos(i.id, i.kudosStatus == 0 ? undefined : 0)">
                      <image class="mr-[4rpx] block h-[30rpx] w-[30rpx]" :src="$iconFormat('icon/thumbsUp4.svg')" mode="scaleToFill" v-if="i.kudosStatus === 0" />
                      <image class="mr-[4rpx] block h-[30rpx] w-[30rpx]" :src="$iconFormat('icon/thumbsUp3.svg')" mode="scaleToFill" v-else />
                      <text class="font-Regular text-[24rpx] font-normal leading-[32rpx] text-w-60">{{ i.dislikeCount || 0 }}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </mescroll-uni>
    </view>

    <!-- 回复输入框 -->
    <view class="w-full shrink-0 bg-deepbg">
      <view class="flex min-h-[116rpx] items-center justify-start pb-[10rpx] pl-3 pr-3 pt-[10rpx]">
        <!-- 回复框 -->
        <!-- <u-input
          class="replyBox h-10 grow bg-w-10"
          :adjustPosition="false"
          :focus="focus"
          :placeholder="replyId ? '回复@' + replyName : '用户期待与您交流'"
          @blur="handleBlur"
          @confirm="handleReplyComment"
          @focus="handleEmojiClose"
          border="none"
          cursorSpacing="10"
          ref="replyRef"
          v-model="replyCon">
          <template #suffix>
            <image class="block h-6 w-6" :src="$iconFormat('icon/emjoy.svg')" @click="handleEmojiOpen" mode="scaleToFill" />
          </template>
        </u-input> -->

        <view class="inputWrap relative h-fit grow">
          <u-textarea
            class="replyBox w-full"
            :adjustPosition="false"
            :focus="focus"
            :placeholder="replyId ? '回复@' + replyName : '用户期待与您交流'"
            :showConfirmBar="false"
            :style="textareaR"
            @blur="handleBlur"
            @focus="handleEmojiClose"
            @linechange="handleLineChange"
            autoHeight
            border="none"
            cursorSpacing="10"
            maxlength="2000"
            ref="replyRef"
            v-model="replyCon"></u-textarea>
          <!-- @confirm="handleReplyComment" -->

          <image class="absolute bottom-0 right-[10rpx] top-0 m-auto block h-6 w-6" :src="$iconFormat('icon/emjoy.svg')" @click="handleEmojiOpen" mode="scaleToFill" />
        </view>

        <!-- 发送 -->
        <u-button
          class="sendBtn"
          type="primary"
          :customStyle="{ ...btnStyle }"
          :hairline="false"
          @click.stop.prevent="handleReplyComment"
          color="linear-gradient(270deg, #C5B2F6 0%, #D0C0FD 100%)"
          iconColor="#282656"
          shape="circle"
          v-if="replyCon">
          发送
        </u-button>

        <!-- 赞/踩 -->
        <view class="ml-[22rpx] flex shrink-0 items-center justify-end" v-else>
          <view class="mr-[22rpx] flex shrink-0 items-center justify-end" @tap="handleKudos(id, commentDetail.kudosStatus == 1 ? null : 1)">
            <image class="mr-[12rpx] block h-[36rpx] w-[36rpx]" :src="$iconFormat('icon/thumbsUp2.svg')" mode="scaleToFill" v-if="commentDetail.kudosStatus === 1" />
            <image class="mr-[12rpx] block h-[36rpx] w-[36rpx]" :src="$iconFormat('icon/thumbsUp1.svg')" mode="scaleToFill" v-else />
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ commentDetail.likeCount || 0 }}</text>
          </view>

          <view class="flex shrink-0 items-center justify-end" @tap="handleKudos(id, commentDetail.kudosStatus == 0 ? null : 0)">
            <image class="mr-[12rpx] block h-[36rpx] w-[36rpx]" :src="$iconFormat('icon/thumbsUp4.svg')" mode="scaleToFill" v-if="commentDetail.kudosStatus === 0" />
            <image class="mr-[12rpx] block h-[36rpx] w-[36rpx]" :src="$iconFormat('icon/thumbsUp3.svg')" mode="scaleToFill" v-else />
            <text class="font-Regular text-[24rpx] font-normal leading-[34rpx] text-w-60">{{ commentDetail.dislikeCount || 0 }}</text>
          </view>
        </view>
      </view>

      <emoji :keyboardH="keyboardH" :show="emojiController" @emoji="handleEmojiChange" />

      <view class="w-full" :style="bheight"></view>
    </view>
  </view>

  <u-toast ref="uToast" />

  <!-- 断网提示 -->
  <network />
</template>

<script lang="ts" setup>
import { $commentInfoSave } from '@/api/base'
import { $commentDetail, $commentListByPage, $commentReply } from '@/api/comment'
import emoji from '@/components/Emoji.vue'
import medal from '@/components/Medal.vue'
import network from '@/components/Network.vue'
import { useGlobalStore } from '@/stores/global'
import useMescroll from '@/uni_modules/mescroll-uni/hooks/useMescroll.js'
import { $back, $checkKudos, $iconFormat, $picFormat, $push, $toast, $topclick } from '@/utils/methods'
import dayjs from 'dayjs'
import _ from 'lodash'

const app: any = getCurrentInstance()?.proxy
const { userInfo, downOpt1, upOpt3 } = storeToRefs(useGlobalStore())
const { mescrollInit, downCallback } = useMescroll(onPageScroll, onReachBottom)

const mescrollObj = ref() // 滚动obj

const id = ref(0) // 评论id
const theaterId = ref(0) // 剧场id
const repertoireId = ref(0) // 剧目id
const type = ref(1) // 1：剧场 2：剧目

const commentDetail = ref<any>([]) // 评论详情
const list = ref<any>('') // 评论列表

const commentParentId = ref(undefined) // 回复评论id
const replyId = ref(undefined) // 回复id
const replyName = ref(undefined) // 回复人
const replyCon = ref('') // 回复内容
const isMerchant = ref(0) // 商家,剧目： -1 剧场： -2
const focus = ref(false)
const emojiController = ref(false) // 表情列表显示控制器

/* 发送按钮样式 */
const btnStyle = ref({
  width: '180rpx',
  height: '62rpx',
  border: '2rpx solid #ffffff',
  color: '#282656',
  fontFamily: 'PingFangSC-Medium, PingFang SC',
  fontSize: '28rpx',
  fontWeight: 600,
  padding: 0
})

/* 底部安全高度 */
const bheight = computed(() => {
  return { height: uni.$u.sys().safeAreaInsets.bottom + 'rpx' }
})
const keyboardH = ref(0) // 键盘高度

const textareaR = ref('border-radius: 999999rpx;')

uni.onKeyboardHeightChange((res: any) => {
  keyboardH.value = res.height
})

onLoad((params: any) => {
  id.value = params.id - 0
  theaterId.value = params.theaterId - 0
  repertoireId.value = params.repertoireId - 0
  type.value = params.commentType - 0
})

/* 加载数据 */
const upCallback = (mescroll: any) => {
  mescrollObj.value = mescroll

  /* 评论详情 */
  $commentDetail(id.value, userInfo.value.id)
    .then((res: any) => {
      if (res.data.userAvatar) res.data.userAvatar = $picFormat(res.data.userAvatar)
      if (res.data.theaterCoverPicture) res.data.theaterCoverPicture = $picFormat(res.data.theaterCoverPicture)
      if (res.data.repertoireCoverPicture) res.data.repertoireCoverPicture = $picFormat(res.data.repertoireCoverPicture)
      if (res.data.createTime) res.data.createTime = dayjs(res.data.createTime).format('YYYY年MM月DD日')
      if (res.data.startTime) res.data.startTime = dayjs(res.data.startTime).format('YYYY年MM月DD日')
      res.data.isOpen = false
      commentDetail.value = res.data

      mescroll.endSuccess(1, false)
    })
    .catch(() => {
      mescroll.endErr() // 请求失败, 结束加载
    })

  /* 评论回复列表 */
  $commentListByPage({
    pageNum: mescroll.num,
    pageSize: mescroll.size,
    flag: type.value === 1 ? 2 : 1,
    userId: userInfo.value.id,
    parentId: id.value || undefined,
    theaterId: theaterId.value || undefined,
    repertoireId: repertoireId.value || undefined
  })
    .then((res: any) => {
      const curPageData = res.data.rows || [] // 当前页数据

      curPageData.map((i: any) => {
        if (i.userMerchantId !== '0') {
          if (i.userMerchantId === '-1') {
            i.userName = i.repertoireName
          } else if (i.userMerchantId === '-2') {
            i.userName = i.theaterName
          }

          if (i.repertoireReplyStatus === 1) {
            i.replyUserId = i.userMerchantId
            i.replyAvatar = i.repertoireCoverPicture
            i.replyName = i.repertoireName
          } else if (i.theaterReplyStatus === 1) {
            i.replyUserId = i.userMerchantId
            i.replyAvatar = i.theaterCoverPicture
            i.replyName = i.theaterName
          }
        }

        if (i.createTime) i.createTime = dayjs(i.createTime).format('YYYY年MM月DD日')
      })

      if (mescroll.num == 1) list.value = [] // 第一页需手动制空列表

      list.value = list.value.concat(curPageData) //追加新数据

      mescroll.endBySize(curPageData.length, res.data.total)
    })
    .catch(() => {
      mescroll.endErr() // 请求失败, 结束加载
    })
}

/* 赞踩 */
const handleKudos = (commentId: number, type: any) => {
  uni.$u.throttle(() => {
    $commentInfoSave({ userId: userInfo.value.id, commentId, type }).then((res: any) => {
      if (commentId === id.value) $checkKudos(commentDetail.value, type)
      else {
        let arr: any = _.cloneDeep(list.value)

        arr.map((i: any) => {
          if (i.id === commentId) {
            $checkKudos(i, type)
          }
        })

        list.value = _.cloneDeep(arr)
      }
    })
  }, 500)
}

/* 回复用户 */
const handleReply = (item: any) => {
  replyCon.value = ''
  commentParentId.value = item.id
  replyId.value = item.replyUserId
  replyName.value = item.replyName
  focus.value = true
  if (item.repertoireReplyStatus === 1) {
    isMerchant.value = -1
  } else if (item.theaterReplyStatus === 1) {
    isMerchant.value = -2
  } else {
    isMerchant.value = 0
  }
}

/* 回复输入框失焦 */
const handleBlur = () => {
  setTimeout(() => {
    if (!emojiController.value) {
      commentParentId.value = undefined
      replyId.value = undefined
      replyName.value = undefined
      replyCon.value = ''
      focus.value = false
    }
  }, 1000)
}

/* 表情列表打开 */
const handleEmojiOpen = () => {
  emojiController.value = !emojiController.value
}

/* 表情列表关闭 */
const handleEmojiClose = () => {
  emojiController.value = false
}

/* 选中表情 */
const handleEmojiChange = (e: any) => {
  replyCon.value += e
}

/* 回复评论 */
const handleReplyComment = () => {
  if (replyCon.value) {
    $commentReply({
      theaterId: commentDetail.value.theaterId || undefined,
      repertoireId: commentDetail.value.repertoireId || undefined,
      repertoireInfoDetailId: commentDetail.value.repertoireInfoDetailId || undefined,
      userId: userInfo.value.id,
      parentId: id.value,
      commentParentId: commentParentId.value || id.value,
      replyId: isMerchant.value < 0 ? undefined : replyId.value || commentDetail.value.userId || undefined,
      userMerchantId: isMerchant.value < 0 ? isMerchant.value : undefined,
      content: replyCon.value
    }).then((res: any) => {
      replyId.value = undefined
      replyName.value = undefined
      replyCon.value = ''
      emojiController.value = false
      // $toast(app, '回复成功')
      mescrollObj.value.resetUpScroll()

      uni.$emit('updateComment')
    })
  } else {
    $toast(app, '回复内容不能为空')
  }
}

/* 行数变化 */
const handleLineChange = (e: any) => {
  if (e.detail.lineCount <= 1) {
    textareaR.value = 'border-radius: 999999rpx;'
  } else {
    textareaR.value = 'border-radius: 20rpx;'
  }
}
</script>

<style lang="scss" scoped>
.pageWrap {
  background-image: url($icon + 'background/history.webp');

  .emptyWrap {
    .u-empty {
      &:deep(.u-empty__text) {
        margin: 0;
        font-family: PingFangSC-Regular, 'PingFang SC';
        font-size: 26rpx !important;
        font-weight: 400;
        line-height: 36rpx;
        color: #fff !important;
        opacity: 0.6;
      }
    }
  }

  .replyBox {
    @apply bg-w-10 pl-[38rpx] pr-[68rpx] #{!important};

    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    height: fit-content;
    min-height: 80rpx !important;
    padding-top: 10rpx !important;
    padding-bottom: 10rpx !important;

    &:deep(.u-textarea__field) {
      width: 100% !important;
      min-height: 36rpx !important;
      max-height: 400rpx;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 28rpx !important;
      font-weight: 400;
      line-height: 36rpx !important;
      color: #fff !important;
      resize: none;
    }

    &:deep(.input-placeholder) {
      padding: 12rpx 0 !important;
      margin-top: 20rpx !important;
      font-family: PingFangSC-Regular, 'PingFang SC';
      font-size: 28rpx !important;
      font-weight: 400;
      line-height: 36rpx !important;
      color: rgb(255 255 255 / 40%) !important;
    }
  }

  .sendBtn {
    margin-left: 20rpx;
  }
}
</style>
