/* 用户提醒功能 API - 简化版 */

/* 获取用户提醒设置 */
export const $getUserReminder = () => uni.$u.http.get('/userReminder/getUserReminder')

/* 保存/更新用户提醒设置 */
export const $saveOrUpdateUserReminder = (data: any) => uni.$u.http.post('/userReminder/saveOrUpdateUserReminder', data)

/* 删除用户提醒设置 */
export const $deleteUserReminder = () => uni.$u.http.delete('/userReminder/deleteUserReminder')

/* 更新看剧基金金额 */
export const $updateTheaterFund = (params: any) => uni.$u.http.post('/userReminder/updateTheaterFund', {}, { params })

// TypeScript 类型定义
export interface UserReminderResponse {
  id: number
  userId: number
  salaryDay: number              // 发薪日 1-31
  showTime: string               // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime: string         // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundCurrentAmount: number // 看剧基金当前金额
  theaterFundTargetAmount: number  // 看剧基金目标金额
  progressPercentage: number       // 完成进度百分比
  createTime: string
}

export interface UserReminderRequest {
  salaryDay?: number              // 发薪日 1-31
  showTime?: string               // 开演时间 "yyyy-MM-dd HH:mm:ss"
  ticketSaleTime?: string         // 开票时间 "yyyy-MM-dd HH:mm:ss"
  theaterFundCurrentAmount?: number // 看剧基金当前金额 0.00-999999.99
  theaterFundTargetAmount?: number  // 看剧基金目标金额 0.00-999999.99
}
