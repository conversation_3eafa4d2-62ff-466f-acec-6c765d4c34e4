{"name": "perform-market", "version": "1.0.0", "scripts": {"dev": "uni -p mp-weixin", "build": "uni build -p mp-weixin", "check": "eslint ./src/**/*.{vue,js,ts,jsx,tsx}", "check:fix": "eslint ./src/**/*.{vue,js,ts,jsx,tsx} --fix", "style:check": "stylelint ./src/**/*.{css,scss,sass,vue}", "style:fix": "stylelint ./src/**/*.{css,scss,sass,vue} --fix", "postinstall": "weapp-tw patch", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-alpha-4000120240201002", "@dcloudio/uni-app-plus": "^3.0.0-4020920240930001", "@dcloudio/uni-components": "^3.0.0-alpha-4000120240201002", "@dcloudio/uni-h5": "^3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-alipay": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-baidu": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-jd": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-kuaishou": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-lark": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-qq": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-toutiao": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-weixin": "^3.0.0-alpha-4000120240201002", "@dcloudio/uni-mp-xhs": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-quickapp-webview": "3.0.0-alpha-4000120240201002", "@jonny1994/qqmap-wx-jssdk": "^1.4.0", "@zebra-ui/swiper": "^2.2.8", "clipboard": "^2.0.11", "dayjs": "^1.11.10", "lodash": "^4.17.21", "pinia": "2.0.36", "three-platformize": "^1.133.3", "uni-crazy-router": "^1.1.3", "vue": "^3.4.21", "vue-i18n": "^9.10.1"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/eslint-parser": "^7.23.10", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-cli-shared": "3.0.0-alpha-4000120240201002", "@dcloudio/uni-stacktracey": "3.0.0-alpha-4000120240201002", "@dcloudio/vite-plugin-uni": "3.0.0-alpha-4000120240201002", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/lodash": "^4.14.202", "@types/node": "^20.11.24", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vue/runtime-core": "^3.4.21", "@vue/tsconfig": "^0.1.3", "autoprefixer": "^10.4.18", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.33.0", "postcss": "^8.4.35", "postcss-html": "^1.6.0", "postcss-rem-to-responsive-pixel": "^5.1.3", "postcss-scss": "^4.0.9", "prettier": "^2.8.8", "prettier-plugin-organize-attributes": "^0.0.5", "prettier-plugin-tailwindcss": "^0.3.0", "regenerator-runtime": "0.12.1", "sass": "^1.77.8", "sass-loader": "^14.2.1", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-recommended-vue": "^1.5.0", "stylelint-config-standard": "^36.0.0", "subfont": "^7.2.1", "tailwindcss": "^3.4.1", "typescript": "^4.9.4", "uni-vite-plugin-h5-prod-effect": "^1.0.1", "unplugin-auto-import": "^0.17.5", "vite": "4.0.3", "vite-plugin-eslint": "^1.8.1", "vue-tsc": "^1.0.24", "weapp-tailwindcss": "^3.0.11"}}